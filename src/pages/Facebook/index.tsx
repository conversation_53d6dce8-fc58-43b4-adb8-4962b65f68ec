import Header from './components/Header';
import { useFBContext } from './context/FbAuthContext';
import EmptyDataView from './views/EmptyDataView';
import Container from './components/Container';
import ConnectFacebookPopup from './components/ConnectFacebokPopup';
import Breadcrumb from '@/components/Breadcrumb';
import { useState } from 'react';
import Modal from '@/components/Modal';
import HistoryFacebook from './components/HistoryFacebook';
import UpdateCustomAudience from './components/UpdateCustomAudience';
import { IAudienceDetail } from '@/types/audience';
import { useTranslation } from 'react-i18next';
import { useQueryClient } from '@tanstack/react-query';
import { QUERY_KEY } from '@/utils/constants';

const FacebookPage = () => {
  const { loading, isAccountSelected } = useFBContext();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const [historyModal, setHistoryModal] = useState<{
    isOpen: boolean;
    audienceDetail: IAudienceDetail | null;
  }>({
    isOpen: false,
    audienceDetail: null,
  });

  const [updateModal, setUpdateModal] = useState<{
    isOpen: boolean;
    audienceDetail: IAudienceDetail | null;
  }>({
    isOpen: false,
    audienceDetail: null,
  });

  const handleOpenHistoryModal = (detail: IAudienceDetail) => {
    setHistoryModal({
      isOpen: true,
      audienceDetail: detail,
    });
  };

  const handleCloseHistoryModal = () => {
    setHistoryModal({
      isOpen: false,
      audienceDetail: null,
    });
  };

  const handleOpenUpdateModal = (detail: IAudienceDetail) => {
    setUpdateModal({
      isOpen: true,
      audienceDetail: detail,
    });
  };

  const handleCloseUpdateModal = () => {
    setUpdateModal({
      isOpen: false,
      audienceDetail: null,
    });
    // Invalidate queries to refresh data
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEY.FACEBOOK_AUDIENCE],
    });
  };

  const handleUpdateAudienceDetail = (jobId: number, updatedDetail: IAudienceDetail) => {
    // Update the audience detail in the query cache
    queryClient.setQueryData([QUERY_KEY.FACEBOOK_AUDIENCE_DETAIL, jobId], {
      data: { data: updatedDetail },
    });
  };

  if (loading) {
    return <ConnectFacebookPopup />;
  }

  return (
    <div className="w-full h-full flex flex-col">
      <Breadcrumb />
      {!isAccountSelected ? (
        <>
          <Header />
          <EmptyDataView />
        </>
      ) : (
        <Container
          onOpenHistoryModal={handleOpenHistoryModal}
          onOpenUpdateModal={handleOpenUpdateModal}
        />
      )}

      {/* History Modal */}
      <Modal
        openModal={historyModal.isOpen}
        onOpenChange={(open) => {
          if (!open) handleCloseHistoryModal();
        }}
        className="max-w-[920px] w-full h-[592px]"
        title={
          <div className="h-[40px]">
            <p className="text-lg font-semibold text-big360Color-neutral-950">
              {t('common.updateHistory')}
            </p>
            <p className="text-sm font-normal text-big360Color-neutral-700">
              {historyModal.audienceDetail?.audience_name || t('common.customAudienceName')}
            </p>
          </div>
        }
      >
        {historyModal.audienceDetail && (
          <HistoryFacebook
            jobId={historyModal.audienceDetail.job_id}
            totalContact={historyModal.audienceDetail.total_records || 0}
            onUpdateAudienceDetail={handleUpdateAudienceDetail}
          />
        )}
      </Modal>

      {/* Update Modal */}
      <Modal
        openModal={updateModal.isOpen}
        onOpenChange={(open) => {
          if (!open) closeUpdateModal();
        }}
        titleAlign={'center'}
        className="max-w-[650px] w-full h-[534px]"
        title={
          <div className="h-[40px]">
            <p className="text-lg font-semibold text-big360Color-neutral-950">
              {t('common.update')} {t('common.facebookAds.customAudience')}
            </p>
            <p className="text-sm font-normal text-big360Color-neutral-700">
              {updateModal.audienceDetail?.audience_name || ''}
            </p>
          </div>
        }
      >
        {updateModal.audienceDetail && (
          <UpdateCustomAudience detail={updateModal.audienceDetail} onClose={closeUpdateModal} />
        )}
      </Modal>
    </div>
  );
};

export default FacebookPage;
