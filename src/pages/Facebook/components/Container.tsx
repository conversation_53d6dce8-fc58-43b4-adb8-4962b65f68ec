import React, { useState } from 'react';
import Navbar from './Navbar';
import { TAB_FACEBOOK } from '@/constants/facebook';
import CustomAudiences from '../views/CustomAudiences';
import Campaign from '../views/Campaign';
import Header from '@/pages/Facebook/components/Header';
import { TFilterAudience } from '@/types/facebook';
import { useFBContext } from '@/pages/Facebook/context/FbAuthContext';
import { IAudienceDetail } from '@/types/audience';

interface ContainerProps {
  onOpenHistoryModal: (detail: IAudienceDetail) => void;
  onOpenUpdateModal: (detail: IAudienceDetail) => void;
}

const Container = ({ onOpenHistoryModal, onOpenUpdateModal }: ContainerProps) => {
  const [tab, setTab] = useState<TAB_FACEBOOK>(TAB_FACEBOOK.AUDIENCES);
  const [filterPayload, setFilterPayload] = useState<TFilterAudience>({
    search: '',
    page: 1,
    limit: 10,
    date_created_from: '',
    date_created_to: '',
  });
  const { listPages } = useFBContext();
  const currentFbAccount = listPages.find((item) => item.selected);

  const renderView = () => {
    switch (tab) {
      case TAB_FACEBOOK.AUDIENCES:
        return (
          <CustomAudiences
            filterPayload={filterPayload}
            setFilterPayload={setFilterPayload}
            onOpenHistoryModal={onOpenHistoryModal}
            onOpenUpdateModal={onOpenUpdateModal}
          />
        );
      case TAB_FACEBOOK.CAMPAIGN:
        return <Campaign />;
      default:
        return null;
    }
  };

  return (
    <>
      <Header filterPayload={filterPayload} ad_Account_id={currentFbAccount?.id ?? ''} />
      <Navbar path={tab} setPath={setTab} />
      {renderView()}
    </>
  );
};

export default Container;
